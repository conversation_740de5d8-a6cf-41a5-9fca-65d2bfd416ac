# Email Configuration for Uptime Tracker
# Copy this file to .env and fill in your email settings

# Email Provider (mailhog, gmail, outlook, custom, or test)
EMAIL_PROVIDER=mailhog

# Email credentials
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password

# For Gmail:
# 1. Enable 2-factor authentication
# 2. Generate an "App Password" (not your regular password)
# 3. Use the app password in EMAIL_PASSWORD

# For Outlook/Hotmail:
# Use your regular email and password

# For custom SMTP (if EMAIL_PROVIDER=custom):
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_SECURE=false

# MailHog mode (EMAIL_PROVIDER=mailhog) - RECOMMENDED FOR TESTING:
# 1. Install MailHog: go install github.com/mailhog/MailHog@latest
# 2. Start MailHog: MailHog
# 3. View emails at: http://localhost:8025
# No real emails sent, all captured locally

# Test mode (EMAIL_PROVIDER=test):
# Uses Ethereal Email for testing - no real emails sent
# Check console for test email URLs
