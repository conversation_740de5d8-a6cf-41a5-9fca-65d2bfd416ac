# Email Configuration for Uptime Tracker
# Copy this file to .env and fill in your email settings

# Email Provider (gmail, outlook, custom, or test)
EMAIL_PROVIDER=test

# Email credentials
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password

# For Gmail:
# 1. Enable 2-factor authentication
# 2. Generate an "App Password" (not your regular password)
# 3. Use the app password in EMAIL_PASSWORD

# For Outlook/Hotmail:
# Use your regular email and password

# For custom SMTP (if EMAIL_PROVIDER=custom):
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_SECURE=false

# Test mode (EMAIL_PROVIDER=test):
# Uses Ethereal Email for testing - no real emails sent
# Check console for test email URLs
