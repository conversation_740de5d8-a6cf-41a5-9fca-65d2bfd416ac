{"name": "uptime-tracker", "version": "1.0.0", "description": "Website uptime monitoring application developed with TDD methodology", "main": "server.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui"}, "keywords": ["uptime", "monitoring", "tdd", "playwright", "express", "handlebars"], "author": "", "license": "MIT", "dependencies": {"@sendgrid/mail": "^8.1.0", "axios": "^1.9.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-handlebars": "^7.1.2", "nodemailer": "^6.9.8", "nodemon": "^3.1.10"}, "devDependencies": {"@playwright/test": "^1.40.0"}}